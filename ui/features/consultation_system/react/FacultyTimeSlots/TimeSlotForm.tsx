import React, { useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { TextInput } from '@instructure/ui-text-input'
import CanvasSelect from '@canvas/instui-bindings/react/Select'
import { Checkbox } from '@instructure/ui-checkbox'
import { TextArea } from '@instructure/ui-text-area'
import { FormFieldGroup } from '@instructure/ui-form-field'
import { Flex } from '@instructure/ui-flex'
import { useTimeSlotFormValidation } from '../hooks/useFormValidation'
import type { FacultyTimeSlot, TimeSlotFormData } from '../types'

interface TimeSlotFormProps {
  daysOfWeek: string[]
  initialData?: FacultyTimeSlot | null
  prefilledData?: Partial<TimeSlotFormData> | null
  onSubmit: (data: TimeSlotFormData) => Promise<void>
  onCancel: () => void
  loading: boolean
}

const TimeSlotForm: React.FC<TimeSlotFormProps> = ({
  daysOfWeek,
  initialData,
  prefilledData,
  onSubmit,
  onCancel,
  loading
}) => {
  const initialFormData: TimeSlotFormData = {
    start_time: prefilledData?.start_time || '',
    end_time: prefilledData?.end_time || '',
    day_of_week: prefilledData?.day_of_week || '',
    is_recurring: prefilledData?.is_recurring ?? true,
    specific_date: prefilledData?.specific_date || '',
    is_available: prefilledData?.is_available ?? true,
    notes: prefilledData?.notes || ''
  }

  const {
    data: formData,
    errors,
    isSubmitting,
    updateField,
    handleSubmit,
    reset
  } = useTimeSlotFormValidation(initialFormData, onSubmit)

  useEffect(() => {
    if (initialData) {
      const updatedData = {
        start_time: initialData.start_time,
        end_time: initialData.end_time,
        day_of_week: initialData.day_of_week,
        is_recurring: initialData.is_recurring,
        specific_date: initialData.specific_date || '',
        is_available: initialData.is_available,
        notes: initialData.notes || ''
      }
      // Update form data when initialData changes
      Object.keys(updatedData).forEach(key => {
        updateField(key as keyof TimeSlotFormData, updatedData[key as keyof TimeSlotFormData])
      })
    } else if (prefilledData) {
      // Update form data when prefilledData changes
      Object.keys(prefilledData).forEach(key => {
        const value = prefilledData[key as keyof TimeSlotFormData]
        if (value !== undefined) {
          updateField(key as keyof TimeSlotFormData, value)
        }
      })
    }
  }, [initialData, prefilledData, updateField])

  // Form submission is handled by the validation hook

  const generateTimeOptions = () => {
    const options = []
    for (let hour = 7; hour <= 20; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
        const displayTime = new Date(`2000-01-01T${timeString}:00`).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })
        options.push({ value: timeString, label: displayTime })
      }
    }
    return options
  }

  const timeOptions = generateTimeOptions()

  return (
    <View as="div" background="secondary" padding="medium" borderRadius="medium">
      <Heading level="h2" margin="0 0 medium 0">
        {initialData ? 'Edit Time Slot' : 'Add New Time Slot'}
      </Heading>

      <form onSubmit={handleSubmit}>
        <FormFieldGroup description="Time Slot Details" layout="stacked">
          <Flex gap="medium">
            <Flex.Item shouldGrow>
              <CanvasSelect
                id="start-time-select"
                label="Start Time"
                value={formData.start_time}
                onChange={(_e: any, value: any) => updateField('start_time', value)}
              >
                {timeOptions.map(option => (
                  <CanvasSelect.Option key={option.value} id={option.value} value={option.value}>
                    {option.label}
                  </CanvasSelect.Option>
                ))}
              </CanvasSelect>
            </Flex.Item>

            <Flex.Item shouldGrow>
              <CanvasSelect
                id="end-time-select"
                label="End Time"
                value={formData.end_time}
                onChange={(_e: any, value: any) => updateField('end_time', value)}
              >
                {timeOptions.map(option => (
                  <CanvasSelect.Option key={option.value} id={option.value} value={option.value}>
                    {option.label}
                  </CanvasSelect.Option>
                ))}
              </CanvasSelect>
            </Flex.Item>
          </Flex>

          <Checkbox
            label="Recurring weekly slot"
            checked={formData.is_recurring}
            onChange={(e) => {
              const isRecurring = e.target.checked
              updateField('is_recurring', isRecurring)
              // Clear specific_date when switching to recurring mode
              if (isRecurring) {
                updateField('specific_date', '')
              }
            }}
          />

          {formData.is_recurring ? (
            <CanvasSelect
              id="day-of-week-select"
              label="Day of Week"
              value={formData.day_of_week}
              onChange={(_e: any, value: any) => updateField('day_of_week', value)}
            >
              {daysOfWeek.map(day => (
                <CanvasSelect.Option key={day} id={day} value={day}>
                  {day}
                </CanvasSelect.Option>
              ))}
            </CanvasSelect>
          ) : (
            <TextInput
              renderLabel="Specific Date"
              value={formData.specific_date}
              onChange={(e) => updateField('specific_date', e.target.value)}
              messages={errors.specific_date ? [{ text: errors.specific_date, type: 'error' }] : []}
            />
          )}

          <Checkbox
            label="Available for booking"
            checked={formData.is_available}
            onChange={(e) => updateField('is_available', e.target.checked)}
          />

          <TextArea
            label="Notes (optional)"
            placeholder="Add any additional notes about this time slot..."
            value={formData.notes}
            onChange={(e) => updateField('notes', e.target.value)}
            height="4rem"
          />
        </FormFieldGroup>

        <Flex margin="large 0 0 0" gap="small" justifyItems="end">
          <Button
            type="button"
            onClick={onCancel}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            color="primary"
            disabled={loading || isSubmitting}
          >
            {loading || isSubmitting ? 'Saving...' : (initialData ? 'Update Time Slot' : 'Create Time Slot')}
          </Button>
        </Flex>
      </form>
    </View>
  )
}

export default TimeSlotForm
